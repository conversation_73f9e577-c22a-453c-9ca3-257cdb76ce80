<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Mailing List FAQ
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Mailing List FAQ
      </h1>


<div class="top-level-extent" id="SEC_Top">

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-General-Questions" href="#General-Questions">1 General Questions</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-What-is-a-mailing-list_003f" href="#What-is-a-mailing-list_003f">1.1 What is a mailing list?</a></li>
    <li><a id="toc-What-type-of-questions-can-I-ask_003f" href="#What-type-of-questions-can-I-ask_003f">1.2 What type of questions can I ask?</a></li>
    <li><a id="toc-How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f-1" href="#How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f-1">1.3 How do I ask a question or send a message to a mailing list?</a></li>
  </ul></li>
  <li><a id="toc-Subscribing-_002f-Unsubscribing" href="#Subscribing-_002f-Unsubscribing">2 Subscribing / Unsubscribing</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-How-do-I-subscribe_003f-1" href="#How-do-I-subscribe_003f-1">2.1 How do I subscribe?</a></li>
    <li><a id="toc-How-do-I-unsubscribe_003f" href="#How-do-I-unsubscribe_003f">2.2 How do I unsubscribe?</a></li>
  </ul></li>
  <li><a id="toc-Moderation-Queue" href="#Moderation-Queue">3 Moderation Queue</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Why-is-my-message-awaiting-moderator-approval_003f-1" href="#Why-is-my-message-awaiting-moderator-approval_003f-1">3.1 Why is my message awaiting moderator approval?</a></li>
    <li><a id="toc-How-long-does-it-take-for-my-message-in-the-moderation-queue-to-be-approved_003f" href="#How-long-does-it-take-for-my-message-in-the-moderation-queue-to-be-approved_003f">3.2 How long does it take for my message in the moderation queue to be approved?</a></li>
    <li><a id="toc-How-do-I-delete-my-message-in-the-moderation-queue_003f-1" href="#How-do-I-delete-my-message-in-the-moderation-queue_003f-1">3.3 How do I delete my message in the moderation queue?</a></li>
  </ul></li>
  <li><a id="toc-Archives" href="#Archives">4 Archives</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Where-are-the-archives_003f-1" href="#Where-are-the-archives_003f-1">4.1 Where are the archives?</a></li>
    <li><a id="toc-How-do-I-reply-to-a-message-in-the-archives_003f" href="#How-do-I-reply-to-a-message-in-the-archives_003f">4.2 How do I reply to a message in the archives?</a></li>
    <li><a id="toc-How-do-I-search-the-archives_003f" href="#How-do-I-search-the-archives_003f">4.3 How do I search the archives?</a></li>
  </ul></li>
  <li><a id="toc-Other" href="#Other">5 Other</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Is-there-an-alternative-to-the-mailing-list_003f" href="#Is-there-an-alternative-to-the-mailing-list_003f">5.1 Is there an alternative to the mailing list?</a></li>
    <li><a id="toc-What-is-top_002dposting_003f-1" href="#What-is-top_002dposting_003f-1">5.2 What is top-posting?</a></li>
    <li><a id="toc-What-is-the-message-size-limit_003f-1" href="#What-is-the-message-size-limit_003f-1">5.3 What is the message size limit?</a></li>
    <li><a id="toc-Where-can-I-upload-sample-files_003f" href="#Where-can-I-upload-sample-files_003f">5.4 Where can I upload sample files?</a></li>
    <li><a id="toc-Will-I-receive-spam-if-I-send-and_002for-subscribe-to-a-mailing-list_003f" href="#Will-I-receive-spam-if-I-send-and_002for-subscribe-to-a-mailing-list_003f">5.5 Will I receive spam if I send and/or subscribe to a mailing list?</a></li>
    <li><a id="toc-How-do-I-filter-mailing-list-messages_003f" href="#How-do-I-filter-mailing-list-messages_003f">5.6 How do I filter mailing list messages?</a></li>
    <li><a id="toc-How-do-I-disable-mail-delivery-without-unsubscribing_003f-1" href="#How-do-I-disable-mail-delivery-without-unsubscribing_003f-1">5.7 How do I disable mail delivery without unsubscribing?</a></li>
    <li><a id="toc-Why-is-the-mailing-list-munging-my-address_003f-1" href="#Why-is-the-mailing-list-munging-my-address_003f-1">5.8 Why is the mailing list munging my address?</a></li>
  </ul></li>
  <li><a id="toc-Rules-and-Etiquette" href="#Rules-and-Etiquette">6 Rules and Etiquette</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-What-are-the-rules-and-the-proper-etiquette_003f" href="#What-are-the-rules-and-the-proper-etiquette_003f">6.1 What are the rules and the proper etiquette?</a></li>
  </ul></li>
  <li><a id="toc-Help" href="#Help">7 Help</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Why-am-I-not-receiving-any-messages_003f" href="#Why-am-I-not-receiving-any-messages_003f">7.1 Why am I not receiving any messages?</a></li>
    <li><a id="toc-Why-are-my-sent-messages-not-showing-up_003f" href="#Why-are-my-sent-messages-not-showing-up_003f">7.2 Why are my sent messages not showing up?</a></li>
    <li><a id="toc-Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f-1" href="#Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f-1">7.3 Why do I keep getting unsubscribed from ffmpeg-devel?</a></li>
    <li><a id="toc-Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f-1" href="#Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f-1">7.4 Who do I contact if I have a problem with the mailing list?</a></li>
  </ul></li>
</ul>
</div>
</div>

<ul class="mini-toc">
<li><a href="#General-Questions" accesskey="1">General Questions</a></li>
<li><a href="#Subscribing-_002f-Unsubscribing" accesskey="2">Subscribing / Unsubscribing</a></li>
<li><a href="#Moderation-Queue" accesskey="3">Moderation Queue</a></li>
<li><a href="#Archives" accesskey="4">Archives</a></li>
<li><a href="#Other" accesskey="5">Other</a></li>
<li><a href="#Rules-and-Etiquette" accesskey="6">Rules and Etiquette</a></li>
<li><a href="#Help" accesskey="7">Help</a></li>
</ul>
<div class="chapter-level-extent" id="General-Questions">
<h2 class="chapter"><span>1 General Questions<a class="copiable-link" href="#General-Questions"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#What-is-a-mailing-list_003f" accesskey="1">What is a mailing list?</a></li>
<li><a href="#What-type-of-questions-can-I-ask_003f" accesskey="2">What type of questions can I ask?</a></li>
<li><a href="#How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f-1" accesskey="3">How do I ask a question or send a message to a mailing list?</a></li>
</ul>
<div class="section-level-extent" id="What-is-a-mailing-list_003f">
<h3 class="section"><span>1.1 What is a mailing list?<a class="copiable-link" href="#What-is-a-mailing-list_003f"> &para;</a></span></h3>

<p>A mailing list is not much different than emailing someone, but the
main difference is that your message is received by everyone who
subscribes to the list. It is somewhat like a forum but in email form.
</p>
<p>See the <a class="url" href="https://lists.ffmpeg.org/pipermail/ffmpeg-user/">ffmpeg-user archives</a>
for examples.
</p>
</div>
<div class="section-level-extent" id="What-type-of-questions-can-I-ask_003f">
<h3 class="section"><span>1.2 What type of questions can I ask?<a class="copiable-link" href="#What-type-of-questions-can-I-ask_003f"> &para;</a></span></h3>

<ul class="itemize mark-bullet">
<li><a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-user/">ffmpeg-user</a>:
For questions involving unscripted usage or compilation of the FFmpeg
command-line tools (<code class="command">ffmpeg</code>, <code class="command">ffprobe</code>, <code class="command">ffplay</code>).

</li><li><a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/libav-user/">libav-user</a>:
For questions involving the FFmpeg libav* libraries (libavcodec,
libavformat, libavfilter, etc).

</li><li><a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-devel/">ffmpeg-devel</a>:
For discussions involving the development of FFmpeg and for submitting
patches. User questions should be asked at ffmpeg-user or libav-user.
</li></ul>

<p>To report a bug see <a class="url" href="https://ffmpeg.org/bugreports.html">https://ffmpeg.org/bugreports.html</a>.
</p>
<p>We cannot provide help for scripts and/or third-party tools.
</p>
<a class="anchor" id="How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f"></a></div>
<div class="section-level-extent" id="How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f-1">
<h3 class="section"><span>1.3 How do I ask a question or send a message to a mailing list?<a class="copiable-link" href="#How-do-I-ask-a-question-or-send-a-message-to-a-mailing-list_003f-1"> &para;</a></span></h3>

<p>First you must <a class="ref" href="#How-do-I-subscribe_003f">subscribe</a>. Then all you have to do is
send an email:
</p>
<ul class="itemize mark-bullet">
<li>Email <a class="email" href="mailto:<EMAIL>"><EMAIL></a> to send a message to the
ffmpeg-user mailing list.

</li><li>Email <a class="email" href="mailto:<EMAIL>"><EMAIL></a> to send a message to the
libav-user mailing list.

</li><li>Email <a class="email" href="mailto:<EMAIL>"><EMAIL></a> to send a message to the
ffmpeg-devel mailing list.
</li></ul>

</div>
</div>
<div class="chapter-level-extent" id="Subscribing-_002f-Unsubscribing">
<h2 class="chapter"><span>2 Subscribing / Unsubscribing<a class="copiable-link" href="#Subscribing-_002f-Unsubscribing"> &para;</a></span></h2>

<a class="anchor" id="How-do-I-subscribe_003f"></a><ul class="mini-toc">
<li><a href="#How-do-I-subscribe_003f-1" accesskey="1">How do I subscribe?</a></li>
<li><a href="#How-do-I-unsubscribe_003f" accesskey="2">How do I unsubscribe?</a></li>
</ul>
<div class="section-level-extent" id="How-do-I-subscribe_003f-1">
<h3 class="section"><span>2.1 How do I subscribe?<a class="copiable-link" href="#How-do-I-subscribe_003f-1"> &para;</a></span></h3>

<p>Email <a class="email" href="mailto:<EMAIL>"><EMAIL></a> with the subject
<em class="emph">subscribe</em>.
</p>
<p>Or visit the <a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-user/">ffmpeg-user mailing list info page</a>
and refer to the <em class="emph">Subscribing to ffmpeg-user</em> section.
</p>
<p>The process is the same for the other mailing lists.
</p>
</div>
<div class="section-level-extent" id="How-do-I-unsubscribe_003f">
<h3 class="section"><span>2.2 How do I unsubscribe?<a class="copiable-link" href="#How-do-I-unsubscribe_003f"> &para;</a></span></h3>

<p>Email <a class="email" href="mailto:<EMAIL>"><EMAIL></a> with subject <em class="emph">unsubscribe</em>.
</p>
<p>Or visit the <a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-user/">ffmpeg-user mailing list info page</a>,
scroll to bottom of page, enter your email address in the box, and click
the <em class="emph">Unsubscribe or edit options</em> button.
</p>
<p>The process is the same for the other mailing lists.
</p>
<p>Please avoid asking a mailing list admin to unsubscribe you unless you
are absolutely unable to do so by yourself. See <a class="ref" href="#Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f">Who do I contact if I have a problem with the mailing list?</a>
</p>
<p>Note that it is possible to temporarily halt message delivery (vacation mode).
See <a class="ref" href="#How-do-I-disable-mail-delivery-without-unsubscribing_003f">How do I disable mail delivery without unsubscribing?</a>
</p>
</div>
</div>
<div class="chapter-level-extent" id="Moderation-Queue">
<h2 class="chapter"><span>3 Moderation Queue<a class="copiable-link" href="#Moderation-Queue"> &para;</a></span></h2>
<a class="anchor" id="Why-is-my-message-awaiting-moderator-approval_003f"></a><ul class="mini-toc">
<li><a href="#Why-is-my-message-awaiting-moderator-approval_003f-1" accesskey="1">Why is my message awaiting moderator approval?</a></li>
<li><a href="#How-long-does-it-take-for-my-message-in-the-moderation-queue-to-be-approved_003f" accesskey="2">How long does it take for my message in the moderation queue to be approved?</a></li>
<li><a href="#How-do-I-delete-my-message-in-the-moderation-queue_003f-1" accesskey="3">How do I delete my message in the moderation queue?</a></li>
</ul>
<div class="section-level-extent" id="Why-is-my-message-awaiting-moderator-approval_003f-1">
<h3 class="section"><span>3.1 Why is my message awaiting moderator approval?<a class="copiable-link" href="#Why-is-my-message-awaiting-moderator-approval_003f-1"> &para;</a></span></h3>

<p>Some messages are automatically held in the <em class="emph">moderation queue</em> and
must be manually approved by a mailing list admin:
</p>
<p>These are:
</p>
<ul class="itemize mark-bullet">
<li>Messages that exceed the <a class="ref" href="#What-is-the-message-size-limit_003f">message size limit</a>.

</li><li>Messages from users whose accounts have been set with the <em class="emph">moderation flag</em>
(very rarely occurs, but may if a user repeatedly ignores the rules
or is abusive towards others).
</li></ul>

</div>
<div class="section-level-extent" id="How-long-does-it-take-for-my-message-in-the-moderation-queue-to-be-approved_003f">
<h3 class="section"><span>3.2 How long does it take for my message in the moderation queue to be approved?<a class="copiable-link" href="#How-long-does-it-take-for-my-message-in-the-moderation-queue-to-be-approved_003f"> &para;</a></span></h3>

<p>The queue is not checked on a regular basis. You can ask on the
<code class="t">#ffmpeg-devel</code> IRC channel on Libera Chat for someone to approve your message.
</p>
<a class="anchor" id="How-do-I-delete-my-message-in-the-moderation-queue_003f"></a></div>
<div class="section-level-extent" id="How-do-I-delete-my-message-in-the-moderation-queue_003f-1">
<h3 class="section"><span>3.3 How do I delete my message in the moderation queue?<a class="copiable-link" href="#How-do-I-delete-my-message-in-the-moderation-queue_003f-1"> &para;</a></span></h3>

<p>You should have received an email with the subject <em class="emph">Your message to &lt;mailing list name&gt; awaits moderator approval</em>.
A link is in the message that will allow you to delete your message
unless a mailing list admin already approved or rejected it.
</p>
</div>
</div>
<div class="chapter-level-extent" id="Archives">
<h2 class="chapter"><span>4 Archives<a class="copiable-link" href="#Archives"> &para;</a></span></h2>

<a class="anchor" id="Where-are-the-archives_003f"></a><ul class="mini-toc">
<li><a href="#Where-are-the-archives_003f-1" accesskey="1">Where are the archives?</a></li>
<li><a href="#How-do-I-reply-to-a-message-in-the-archives_003f" accesskey="2">How do I reply to a message in the archives?</a></li>
<li><a href="#How-do-I-search-the-archives_003f" accesskey="3">How do I search the archives?</a></li>
</ul>
<div class="section-level-extent" id="Where-are-the-archives_003f-1">
<h3 class="section"><span>4.1 Where are the archives?<a class="copiable-link" href="#Where-are-the-archives_003f-1"> &para;</a></span></h3>

<p>See the <em class="emph">Archives</em> section on the <a class="url" href="https://ffmpeg.org/contact.html">FFmpeg Contact</a>
page for links to all FFmpeg mailing list archives.
</p>
<p>Note that the archives are split by month. Discussions that span
several months will be split into separate months in the archives.
</p>
</div>
<div class="section-level-extent" id="How-do-I-reply-to-a-message-in-the-archives_003f">
<h3 class="section"><span>4.2 How do I reply to a message in the archives?<a class="copiable-link" href="#How-do-I-reply-to-a-message-in-the-archives_003f"> &para;</a></span></h3>

<p>Click the email link at the top of the message just under the subject
title. The link will provide the proper headers to keep the message
within the thread.
</p>
<p>Note that you must be subscribed to send a message to the ffmpeg-user or
libav-user mailing lists.
</p>
</div>
<div class="section-level-extent" id="How-do-I-search-the-archives_003f">
<h3 class="section"><span>4.3 How do I search the archives?<a class="copiable-link" href="#How-do-I-search-the-archives_003f"> &para;</a></span></h3>

<p>Perform a site search using your favorite search engine. Example:
</p>
<p><code class="t">site:lists.ffmpeg.org/pipermail/ffmpeg-user/ &quot;search term&quot;</code>
</p>
</div>
</div>
<div class="chapter-level-extent" id="Other">
<h2 class="chapter"><span>5 Other<a class="copiable-link" href="#Other"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#Is-there-an-alternative-to-the-mailing-list_003f" accesskey="1">Is there an alternative to the mailing list?</a></li>
<li><a href="#What-is-top_002dposting_003f-1" accesskey="2">What is top-posting?</a></li>
<li><a href="#What-is-the-message-size-limit_003f-1" accesskey="3">What is the message size limit?</a></li>
<li><a href="#Where-can-I-upload-sample-files_003f" accesskey="4">Where can I upload sample files?</a></li>
<li><a href="#Will-I-receive-spam-if-I-send-and_002for-subscribe-to-a-mailing-list_003f" accesskey="5">Will I receive spam if I send and/or subscribe to a mailing list?</a></li>
<li><a href="#How-do-I-filter-mailing-list-messages_003f" accesskey="6">How do I filter mailing list messages?</a></li>
<li><a href="#How-do-I-disable-mail-delivery-without-unsubscribing_003f-1" accesskey="7">How do I disable mail delivery without unsubscribing?</a></li>
<li><a href="#Why-is-the-mailing-list-munging-my-address_003f-1" accesskey="8">Why is the mailing list munging my address?</a></li>
</ul>
<div class="section-level-extent" id="Is-there-an-alternative-to-the-mailing-list_003f">
<h3 class="section"><span>5.1 Is there an alternative to the mailing list?<a class="copiable-link" href="#Is-there-an-alternative-to-the-mailing-list_003f"> &para;</a></span></h3>

<p>You can ask for help in the official <code class="t">#ffmpeg</code> IRC channel on Libera Chat.
</p>
<p>Some users prefer the third-party <a class="url" href="http://www.ffmpeg-archive.org/">Nabble</a>
interface which presents the mailing lists in a typical forum layout.
</p>
<p>There are also numerous third-party help sites such as
<a class="url" href="https://superuser.com/tags/ffmpeg">Super User</a> and
<a class="url" href="https://www.reddit.com/r/ffmpeg/">r/ffmpeg on reddit</a>.
</p>
<a class="anchor" id="What-is-top_002dposting_003f"></a></div>
<div class="section-level-extent" id="What-is-top_002dposting_003f-1">
<h3 class="section"><span>5.2 What is top-posting?<a class="copiable-link" href="#What-is-top_002dposting_003f-1"> &para;</a></span></h3>

<p>See <a class="url" href="https://en.wikipedia.org/wiki/Posting_style#Top-posting">https://en.wikipedia.org/wiki/Posting_style#Top-posting</a>.
</p>
<p>Instead, use trimmed interleaved/inline replies (<a class="url" href="https://lists.ffmpeg.org/pipermail/ffmpeg-user/2017-April/035849.html">example</a>).
</p>
<a class="anchor" id="What-is-the-message-size-limit_003f"></a></div>
<div class="section-level-extent" id="What-is-the-message-size-limit_003f-1">
<h3 class="section"><span>5.3 What is the message size limit?<a class="copiable-link" href="#What-is-the-message-size-limit_003f-1"> &para;</a></span></h3>

<p>The message size limit is 1000 kilobytes. Please provide links to larger files
instead of attaching them.
</p>
</div>
<div class="section-level-extent" id="Where-can-I-upload-sample-files_003f">
<h3 class="section"><span>5.4 Where can I upload sample files?<a class="copiable-link" href="#Where-can-I-upload-sample-files_003f"> &para;</a></span></h3>

<p>Anywhere that is not too annoying for us to use.
</p>
<p>Google Drive and Dropbox are acceptable if you need a file host, and
<a class="url" href="https://0x0.st/">0x0.st</a> is good for files under 256 MiB.
</p>
<p>Small, short samples are preferred if possible.
</p>
</div>
<div class="section-level-extent" id="Will-I-receive-spam-if-I-send-and_002for-subscribe-to-a-mailing-list_003f">
<h3 class="section"><span>5.5 Will I receive spam if I send and/or subscribe to a mailing list?<a class="copiable-link" href="#Will-I-receive-spam-if-I-send-and_002for-subscribe-to-a-mailing-list_003f"> &para;</a></span></h3>

<p>Highly unlikely.
</p>
<ul class="itemize mark-bullet">
<li>The list of subscribed users is not public.

</li><li>Email addresses in the archives are obfuscated.

</li><li>Several unique test email accounts were utilized and none have yet
received any spam.
</li></ul>

<p>However, you may see a spam in the mailing lists on rare occasions:
</p>
<ul class="itemize mark-bullet">
<li>Spam in the moderation queue may be accidentally approved due to human
error.

</li><li>There have been a few messages from subscribed users who had their own
email addresses hacked and spam messages from (or appearing to be from)
the hacked account were sent to their contacts (a mailing list being a
contact in these cases).

</li><li>If you are subscribed to the bug tracker mailing list (ffmpeg-trac) you
may see the occasional spam as a false bug report, but we take measures
to try to prevent this.
</li></ul>

</div>
<div class="section-level-extent" id="How-do-I-filter-mailing-list-messages_003f">
<h3 class="section"><span>5.6 How do I filter mailing list messages?<a class="copiable-link" href="#How-do-I-filter-mailing-list-messages_003f"> &para;</a></span></h3>

<p>Use the <em class="emph">List-Id</em>. For example, the ffmpeg-user mailing list is
<code class="t">ffmpeg-user.ffmpeg.org</code>. You can view the List-Id in the raw message
or headers.
</p>
<p>You can then filter the mailing list messages to their own folder.
</p>
<a class="anchor" id="How-do-I-disable-mail-delivery-without-unsubscribing_003f"></a></div>
<div class="section-level-extent" id="How-do-I-disable-mail-delivery-without-unsubscribing_003f-1">
<h3 class="section"><span>5.7 How do I disable mail delivery without unsubscribing?<a class="copiable-link" href="#How-do-I-disable-mail-delivery-without-unsubscribing_003f-1"> &para;</a></span></h3>

<p>Sometimes you may want to temporarily stop receiving all mailing list
messages. This &quot;vacation mode&quot; is simple to do:
</p>
<ol class="enumerate">
<li> Go to the <a class="url" href="https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-user/">ffmpeg-user mailing list info page</a>

</li><li> Enter your email address in the box at very bottom of the page and click the
<em class="emph">Unsubscribe or edit options</em> box.

</li><li> Enter your password and click the <em class="emph">Log in</em> button.

</li><li> Look for the <em class="emph">Mail delivery</em> option. Here you can disable/enable mail
delivery. If you check <em class="emph">Set globally</em> it will apply your choice to all
other FFmpeg mailing lists you are subscribed to.
</li></ol>

<p>Alternatively, from your subscribed address, send a message to <a class="email" href="mailto:<EMAIL>"><EMAIL></a>
with the subject <em class="emph">set delivery off</em>. To re-enable mail delivery send a
message to <a class="email" href="mailto:<EMAIL>"><EMAIL></a> with the subject
<em class="emph">set delivery on</em>.
</p>
<a class="anchor" id="Why-is-the-mailing-list-munging-my-address_003f"></a></div>
<div class="section-level-extent" id="Why-is-the-mailing-list-munging-my-address_003f-1">
<h3 class="section"><span>5.8 Why is the mailing list munging my address?<a class="copiable-link" href="#Why-is-the-mailing-list-munging-my-address_003f-1"> &para;</a></span></h3>

<p>This is due to subscribers that use an email service with a DMARC reject policy
which adds difficulties to mailing list operators.
</p>
<p>The mailing list must re-write (munge) the <em class="emph">From:</em> header for such users;
otherwise their email service will reject and bounce the message resulting in
automatic unsubscribing from the mailing list.
</p>
<p>When sending a message these users will see <em class="emph">via &lt;mailing list name&gt;</em>
added to their name and the <em class="emph">From:</em> address munged to the address of
the particular mailing list.
</p>
<p>If you want to avoid this then please use a different email service.
</p>
<p>Note that ffmpeg-devel does not apply any munging as it causes issues with
patch authorship. As a result users with an email service with a DMARC reject
policy may be automatically unsubscribed due to rejected and bounced messages.
</p>
</div>
</div>
<div class="chapter-level-extent" id="Rules-and-Etiquette">
<h2 class="chapter"><span>6 Rules and Etiquette<a class="copiable-link" href="#Rules-and-Etiquette"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#What-are-the-rules-and-the-proper-etiquette_003f" accesskey="1">What are the rules and the proper etiquette?</a></li>
</ul>
<div class="section-level-extent" id="What-are-the-rules-and-the-proper-etiquette_003f">
<h3 class="section"><span>6.1 What are the rules and the proper etiquette?<a class="copiable-link" href="#What-are-the-rules-and-the-proper-etiquette_003f"> &para;</a></span></h3>

<p>There may seem to be many things to remember, but we want to help and
following these guidelines will allow you to get answers more quickly
and help avoid getting ignored.
</p>
<ul class="itemize mark-bullet">
<li>Always show your actual, unscripted <code class="command">ffmpeg</code> command and the
complete, uncut console output from your command.

</li><li>Use the most simple and minimal command that still shows the issue you
are encountering.

</li><li>Provide all necessary information so others can attempt to duplicate
your issue. This includes the actual command, complete uncut console
output, and any inputs that are required to duplicate the issue.

</li><li>Use the latest <code class="command">ffmpeg</code> build you can get. See the <a class="url" href="https://ffmpeg.org/download.html">FFmpeg Download</a>
page for links to recent builds for Linux, macOS, and Windows. Or
compile from the current git master branch.

</li><li>Avoid <a class="url" href="https://en.wikipedia.org/wiki/Posting_style#Top-posting">top-posting</a>.
Also see <a class="ref" href="#What-is-top_002dposting_003f">What is top-posting?</a>

</li><li>Avoid hijacking threads. Thread hijacking is replying to a message and
changing the subject line to something unrelated to the original thread.
Most email clients will still show the renamed message under the
original thread. This can be confusing and these types of messages are
often ignored.

</li><li>Do not send screenshots. Copy and paste console text instead of making
screenshots of the text.

</li><li>Avoid sending email disclaimers and legalese if possible as this is a
public list.

</li><li>Avoid using the <code class="code">-loglevel debug</code>, <code class="code">-loglevel quiet</code>, and
<code class="command">-hide_banner</code> options unless requested to do so.

</li><li>If you attach files avoid compressing small files. Uncompressed is
preferred.

</li><li>Please do not send HTML-only messages. The mailing list will ignore the
HTML component of your message. Most mail clients will automatically
include a text component: this is what the mailing list will use.

</li><li>Configuring your mail client to break lines after 70 or so characters is
recommended.

</li><li>Avoid sending the same message to multiple mailing lists.

</li><li>Please follow our <a class="url" href="https://ffmpeg.org/community.html#Code-of-conduct">Code of Conduct</a>.
</li></ul>

</div>
</div>
<div class="chapter-level-extent" id="Help">
<h2 class="chapter"><span>7 Help<a class="copiable-link" href="#Help"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#Why-am-I-not-receiving-any-messages_003f" accesskey="1">Why am I not receiving any messages?</a></li>
<li><a href="#Why-are-my-sent-messages-not-showing-up_003f" accesskey="2">Why are my sent messages not showing up?</a></li>
<li><a href="#Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f-1" accesskey="3">Why do I keep getting unsubscribed from ffmpeg-devel?</a></li>
<li><a href="#Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f-1" accesskey="4">Who do I contact if I have a problem with the mailing list?</a></li>
</ul>
<div class="section-level-extent" id="Why-am-I-not-receiving-any-messages_003f">
<h3 class="section"><span>7.1 Why am I not receiving any messages?<a class="copiable-link" href="#Why-am-I-not-receiving-any-messages_003f"> &para;</a></span></h3>

<p>Some email providers have blacklists or spam filters that block or mark
the mailing list messages as false positives. Unfortunately, the user is
often not aware of this and is often out of their control.
</p>
<p>When possible we attempt to notify the provider to be removed from the
blacklists or filters.
</p>
</div>
<div class="section-level-extent" id="Why-are-my-sent-messages-not-showing-up_003f">
<h3 class="section"><span>7.2 Why are my sent messages not showing up?<a class="copiable-link" href="#Why-are-my-sent-messages-not-showing-up_003f"> &para;</a></span></h3>

<p>Excluding <a class="ref" href="#Why-is-my-message-awaiting-moderator-approval_003f">messages that are held in the moderation queue</a>
there are a few other reasons why your messages may fail to appear:
</p>
<ul class="itemize mark-bullet">
<li>HTML-only messages are ignored by the mailing lists. Most mail clients
automatically include a text component alongside HTML email: this is what
the mailing list will use. If it does not then consider your client to be
broken, because sending a text component along with the HTML component to
form a multi-part message is recommended by email standards.

</li><li>Check your spam folder.
</li></ul>

<a class="anchor" id="Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f"></a></div>
<div class="section-level-extent" id="Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f-1">
<h3 class="section"><span>7.3 Why do I keep getting unsubscribed from ffmpeg-devel?<a class="copiable-link" href="#Why-do-I-keep-getting-unsubscribed-from-ffmpeg_002ddevel_003f-1"> &para;</a></span></h3>

<p>Users with an email service that has a DMARC reject or quarantine policy may be
automatically unsubscribed from the ffmpeg-devel mailing list due to the mailing
list messages being continuously rejected and bounced back.
</p>
<p>Consider using a different email service.
</p>
<a class="anchor" id="Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f"></a></div>
<div class="section-level-extent" id="Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f-1">
<h3 class="section"><span>7.4 Who do I contact if I have a problem with the mailing list?<a class="copiable-link" href="#Who-do-I-contact-if-I-have-a-problem-with-the-mailing-list_003f-1"> &para;</a></span></h3>

<p>Send a message to <a class="email" href="mailto:<EMAIL>"><EMAIL></a>.
</p>
</div>
</div>
</div>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
